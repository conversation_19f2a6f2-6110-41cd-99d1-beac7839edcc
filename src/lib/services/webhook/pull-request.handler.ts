/**
 * Pull Request Webhook Handler
 * Handles pull request webhook events with proper validation, processing, and external API integration
 */

import { z } from 'zod';
import { WebhookSyncResult, ProcessedWebhookData } from '@/src/lib/types/webhooks';
import { webhookValidationService, PullRequestPayloadSchema } from './validation.service';
import { webhookMetricsService } from './metrics.service';
import { ExternalAPIService } from '../external-api';
import { GitHubStatusService } from '../github-status';
import { errorHandlingService, createErrorContext } from '../error-handling';

/**
 * Pull Request processing configuration
 */
interface PRProcessingConfig {
  cooldownMs: number;
  syncCooldownMs: number;
  enableExternalAPI: boolean;
  enableStatusChecks: boolean;
}

const DEFAULT_PR_CONFIG: PRProcessingConfig = {
  cooldownMs: 5 * 60 * 1000, // 5 minutes
  syncCooldownMs: 60 * 1000, // 1 minute for sync events
  enableExternalAPI: true,
  enableStatusChecks: true,
};

/**
 * Pull Request Webhook Handler
 */
export class PullRequestWebhookHandler {
  private externalAPI = new ExternalAPIService();
  private githubStatusService = new GitHubStatusService();
  private recentlyProcessedPRs = new Map<string, number>();
  private config: PRProcessingConfig;

  constructor(config: Partial<PRProcessingConfig> = {}) {
    this.config = { ...DEFAULT_PR_CONFIG, ...config };
  }

  /**
   * Handle pull request webhook event
   */
  async handlePullRequestEvent(
    payload: unknown,
    headers?: { event: string; delivery: string }
  ): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const result: WebhookSyncResult = {
      success: false,
      action: 'pull_request.unknown',
      repositoriesAffected: 0,
      errors: [],
    };

    try {
      // Validate payload
      const repositoryId = this.extractRepositoryId(payload);
      const validation = webhookValidationService.validatePullRequestPayload(payload, repositoryId);

      if (!validation.isValid) {
        result.errors = validation.errors;
        if (validation.rateLimited) {
          result.action = 'pull_request.rate_limited';
        }
        this.recordMetrics(result, startTime);
        return result;
      }

      const validatedPayload = validation.data!;
      result.action = `pull_request.${validatedPayload.action}`;

      // Handle different PR actions
      if (this.shouldProcessPRAction(validatedPayload.action)) {
        await this.processActionablePR(validatedPayload, headers, result);
      } else {
        await this.processBasicPR(validatedPayload, headers, result);
      }

      result.success = result.errors.length === 0;
      this.recordMetrics(result, startTime);
      return result;

    } catch (error) {
      return this.handleError(error, payload, startTime, result);
    }
  }

  /**
   * Check if PR action requires full processing
   */
  private shouldProcessPRAction(action: string): boolean {
    return ['opened', 'reopened', 'synchronize'].includes(action);
  }

  /**
   * Process actionable PR events (opened, reopened, synchronize)
   */
  private async processActionablePR(
    payload: z.infer<typeof PullRequestPayloadSchema>,
    headers: { event: string; delivery: string } | undefined,
    result: WebhookSyncResult
  ): Promise<void> {
    const repoId = payload.repository.id.toString();
    const prNumber = payload.pull_request.number;

    // Check cooldown period
    // if (this.isPRRecentlyProcessed(repoId, prNumber, payload.action)) {
    //   result.errors.push(`PR ${prNumber} was recently processed, skipping to prevent duplicate processing`);
    //   return;
    // }

    // Mark as processed
    this.markPRAsProcessed(repoId, prNumber);

    // Map PR data
    const prData = this.mapPullRequestData(payload, true, headers);
    console.log(`Pull Request ${payload.action}:`, prData);

    // Create GitHub status check
    if (this.config.enableStatusChecks) {
      await this.createGitHubStatus(payload);
    }

    // Send to external API
    if (this.config.enableExternalAPI) {
      await this.sendToExternalAPI(prData, payload.action);
    }

    result.repositoriesAffected = 1;
  }

  /**
   * Process basic PR events (other actions)
   */
  private async processBasicPR(
    payload: z.infer<typeof PullRequestPayloadSchema>,
    headers: { event: string; delivery: string } | undefined,
    result: WebhookSyncResult
  ): Promise<void> {
    const prData = this.mapPullRequestData(payload, false, headers);
    console.log(`Pull Request ${payload.action}:`, prData);
    result.repositoriesAffected = 1;
  }

  /**
   * Check if PR was recently processed
   */
  private isPRRecentlyProcessed(repoId: string, prNumber: number, action: string): boolean {
    const key = `${repoId}:${prNumber}`;
    const lastProcessed = this.recentlyProcessedPRs.get(key);
    
    if (!lastProcessed) return false;

    // Use shorter cooldown for synchronize events
    const cooldown = action === 'synchronize' ? this.config.syncCooldownMs : this.config.cooldownMs;
    return (Date.now() - lastProcessed) < cooldown;
  }

  /**
   * Mark PR as processed
   */
  private markPRAsProcessed(repoId: string, prNumber: number): void {
    const key = `${repoId}:${prNumber}`;
    this.recentlyProcessedPRs.set(key, Date.now());

    // Clean up old entries periodically
    if (this.recentlyProcessedPRs.size > 1000) {
      this.cleanupOldProcessedPRs();
    }
  }

  /**
   * Clean up old processed PR entries
   */
  private cleanupOldProcessedPRs(): void {
    const cutoff = Date.now() - (this.config.cooldownMs * 2);
    for (const [key, timestamp] of this.recentlyProcessedPRs.entries()) {
      if (timestamp < cutoff) {
        this.recentlyProcessedPRs.delete(key);
      }
    }
  }

  /**
   * Map pull request data for external API
   */
  private mapPullRequestData(
    payload: z.infer<typeof PullRequestPayloadSchema>,
    includeExtended = false,
    headers?: { event: string; delivery: string }
  ): ProcessedWebhookData {
    const baseData: ProcessedWebhookData = {
      id: payload.pull_request.id,
      number: payload.pull_request.number,
      title: payload.pull_request.title,
      state: payload.pull_request.state,
      action: payload.action,
      repository: payload.repository.full_name,
      author: payload.pull_request.user.login,
      sender: payload.sender.login,
      github_event: headers?.event || 'pull_request',
      github_delivery: headers?.delivery || 'unknown',
    };

    if (includeExtended) {
      return {
        ...baseData,
        url: payload.pull_request.html_url,
        base_branch: payload.pull_request.base.ref,
        head_branch: payload.pull_request.head.ref,
        draft: payload.pull_request.draft,
        mergeable: payload.pull_request.mergeable ?? undefined,
        created_at: payload.pull_request.created_at,
        updated_at: payload.pull_request.updated_at,
        diff_url: payload.pull_request.diff_url,
        patch_url: payload.pull_request.patch_url,
        commits: payload.pull_request.commits,
        additions: payload.pull_request.additions,
        deletions: payload.pull_request.deletions,
        changed_files: payload.pull_request.changed_files,
        commits_url: payload.pull_request.commits_url,
        review_comments_url: payload.pull_request.review_comments_url,
        statuses_url: payload.pull_request.statuses_url,
      };
    }

    return baseData;
  }

  /**
   * Create GitHub status check
   */
  private async createGitHubStatus(payload: z.infer<typeof PullRequestPayloadSchema>): Promise<void> {
    try {
      const [owner, repo] = payload.repository.full_name.split('/');
      const installationId = await this.githubStatusService.getInstallationIdForRepo(owner, repo);

      if (installationId) {
        await this.githubStatusService.setPendingStatus(
          installationId,
          owner,
          repo,
          payload.pull_request.head.sha,
          payload.pull_request.number
        );
      }
    } catch (error) {
      console.error('Error setting GitHub status:', error);
      // Don't fail webhook processing if status creation fails
    }
  }

  /**
   * Send data to external API
   */
  private async sendToExternalAPI(prData: ProcessedWebhookData, action: string): Promise<void> {
    try {
      const apiSuccess = await this.externalAPI.sendPullRequestDataWithRetry(prData);
      if (!apiSuccess) {
        console.warn(`Failed to send PR ${action} data to external API after retries`);
      }
    } catch (error) {
      console.error('Unexpected error in external API call:', error);
    }
  }

  /**
   * Extract repository ID from payload
   */
  private extractRepositoryId(payload: unknown): string | undefined {
    if (payload && typeof payload === 'object' && 'repository' in payload) {
      const repo = (payload as any).repository;
      return repo?.id?.toString();
    }
    return undefined;
  }

  /**
   * Record metrics for the webhook processing
   */
  private recordMetrics(result: WebhookSyncResult, startTime: number): void {
    const duration = Date.now() - startTime;
    webhookMetricsService.recordMetric({
      action: result.action,
      success: result.success,
      duration,
      repositoriesAffected: result.repositoriesAffected,
      errorType: result.errors.length > 0 ? 'processing_error' : undefined,
    });
  }

  /**
   * Handle errors during processing
   */
  private handleError(
    error: unknown,
    payload: unknown,
    startTime: number,
    result: WebhookSyncResult
  ): WebhookSyncResult {
    const duration = Date.now() - startTime;
    const repositoryId = this.extractRepositoryId(payload);

    const context = createErrorContext(
      undefined, // userId
      undefined, // organizationId
      undefined, // installationId
      repositoryId,
      'pull_request_webhook'
    );

    const platyfendError = errorHandlingService.handleWebhookError(error, context);
    errorHandlingService.logError(platyfendError);

    result.success = false;
    result.errors.push(platyfendError.message);
    result.metrics = {
      duration,
      timestamp: new Date(),
    };

    webhookMetricsService.recordMetric({
      action: result.action,
      success: false,
      duration,
      repositoryId,
      errorType: platyfendError.type,
    });

    return result;
  }
}

// Export singleton instance
export const pullRequestWebhookHandler = new PullRequestWebhookHandler();
